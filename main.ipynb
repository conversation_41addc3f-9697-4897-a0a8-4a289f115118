import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

import sys
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from datasets import load_dataset

def load_datasets():
    datasets = {
        # Corrected line: specify the configuration name, e.g., 'explicit_questions'
        # You may also need to select a split within that configuration, like 'train'
        "newton": load_dataset("NEWTONReasoning/NEWTON", name="explicit_questions", split="train"),

        # The other datasets should load correctly if they don't have the same issue
        "physical_reasoning": load_dataset("grimulkan/physical-reasoning", split="train"), # Assuming 'train' split
        "prost": load_dataset("corypaik/prost", split="test"), # Assuming 'test' split
        "piqa": load_dataset("ybisk/piqa", split="train") # Assuming 'train' split
    }
    return datasets

def load_datasets():
    datasets = {
        # Corrected line: specify the configuration name, e.g., 'explicit_questions'
        # You may also need to select a split within that configuration, like 'train'
        "newton": load_dataset("NEWTONReasoning/NEWTON", name="explicit_questions", split="train"),

        # The other datasets should load correctly if they don't have the same issue
        "physical_reasoning": load_dataset("grimulkan/physical-reasoning", split="train"), # Assuming 'train' split
        "prost": load_dataset("corypaik/prost", split="test"), # Assuming 'test' split
        "piqa": load_dataset("ybisk/piqa", split="train") # Assuming 'train' split
    }
    return datasets